<?php $__env->startSection('title', '<PERSON><PERSON><PERSON><PERSON> - Dashboard Admin'); ?>
<?php $__env->startSection('page-title', 'Penerimaan Barang'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Penerimaan Barang</h1>
                    <p class="text-gray-600 mt-1">Kelola penerimaan barang dari supplier</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total'])); ?></div>
            <div class="admin-dashboard-stat-label">Total Pengiriman</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e(number_format($stats['pending'])); ?></div>
            <div class="admin-dashboard-stat-label">Menunggu Penerimaan</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e(number_format($stats['received'])); ?></div>
            <div class="admin-dashboard-stat-label">Diterima</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e(number_format($stats['partial'])); ?></div>
            <div class="admin-dashboard-stat-label">Diterima Sebagian</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month" 
                           name="month" 
                           value="<?php echo e($filterMonth); ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>" 
                           placeholder="Cari supplier atau produk..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Semua Status</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="received" <?php echo e(request('status') === 'received' ? 'selected' : ''); ?>>Diterima</option>
                        <option value="partial" <?php echo e(request('status') === 'partial' ? 'selected' : ''); ?>>Sebagian</option>
                        <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Dibatalkan</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Deliveries Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Pengiriman</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Supplier</th>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Kirim</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->supplier->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($delivery->supplier->contact_person); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->product->name); ?></div>
                                <?php if($delivery->unit_price): ?>
                                <div class="text-sm text-gray-500">Rp <?php echo e(number_format($delivery->unit_price, 0, ',', '.')); ?>/unit</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($delivery->quantity)); ?></div>
                                <?php if($delivery->received_quantity): ?>
                                <div class="text-sm text-gray-500">Diterima: <?php echo e(number_format($delivery->received_quantity)); ?></div>
                                <?php endif; ?>
                                <?php if($delivery->total_price): ?>
                                <div class="text-xs text-gray-400">Total: Rp <?php echo e(number_format($delivery->total_price, 0, ',', '.')); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(auth()->user()->formatDate($delivery->delivery_date)); ?></div>
                                <?php if($delivery->received_date): ?>
                                <div class="text-sm text-gray-500">Diterima: <?php echo e(auth()->user()->formatDate($delivery->received_date)); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    <?php if($delivery->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($delivery->status === 'received'): ?> bg-green-100 text-green-800
                                    <?php elseif($delivery->status === 'partial'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($delivery->status === 'cancelled'): ?> bg-red-100 text-red-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php if($delivery->status === 'pending'): ?> Pending
                                    <?php elseif($delivery->status === 'received'): ?> Diterima
                                    <?php elseif($delivery->status === 'partial'): ?> Sebagian
                                    <?php elseif($delivery->status === 'cancelled'): ?> Dibatalkan
                                    <?php else: ?> <?php echo e(ucfirst($delivery->status)); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.supplier-deliveries.show', $delivery)); ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    <?php if($delivery->status === 'pending'): ?>
                                    <button onclick="openReceiveModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->quantity); ?>')"
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Terima
                                    </button>
                                    <button onclick="openCancelModal('<?php echo e($delivery->id); ?>')"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Batal
                                    </button>
                                    <?php elseif(in_array($delivery->status, ['received', 'partial'])): ?>
                                    <button onclick="openReturnModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name); ?>', '<?php echo e($delivery->received_quantity ?? $delivery->quantity); ?>')"
                                            class="text-orange-600 hover:text-orange-800 text-sm font-medium">
                                        Retur
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman</p>
                                    <p>Pengiriman dari supplier akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($deliveries->hasPages()): ?>
            <div class="mt-6">
                <?php echo e($deliveries->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Receive Modal -->
<div id="receiveModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Terima Pengiriman</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeReceiveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="receiveForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <label for="received_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah Diterima <span class="text-red-500">*</span>
                        </label>
                        <input type="number" 
                               id="received_quantity" 
                               name="received_quantity" 
                               min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Masukkan jumlah yang diterima"
                               required>
                        <p class="mt-1 text-sm text-gray-500">Maksimal: <span id="maxQuantity"></span></p>
                    </div>
                    
                    <div>
                        <label for="receive_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="receive_notes" 
                                  name="notes" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Tambahkan catatan penerimaan..."></textarea>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeReceiveModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Terima Barang
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Cancel Modal -->
<div id="cancelModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Batalkan Pengiriman</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeCancelModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="cancelForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="space-y-4">
                    <p class="text-gray-700">Yakin ingin membatalkan pengiriman ini?</p>
                    
                    <div>
                        <label for="cancel_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Pembatalan (Opsional)
                        </label>
                        <textarea id="cancel_notes" 
                                  name="notes" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Jelaskan alasan pembatalan..."></textarea>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeCancelModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                    Batalkan Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Return Modal -->
<div id="returnModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Buat Retur ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeReturnModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="returnForm" method="POST" action="<?php echo e(route('admin.returns.store')); ?>">
            <?php echo csrf_field(); ?>
            <input type="hidden" id="return_delivery_id" name="delivery_id">
            <div class="admin-dashboard-modal-body">
                <div class="space-y-4">
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-orange-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-orange-800">
                                <p class="font-medium">Retur Produk</p>
                                <p>Buat retur untuk produk <strong id="returnProductName"></strong> yang memiliki masalah kualitas atau kerusakan.</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="return_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah Retur (Maksimal: <span id="maxReturnQuantity"></span>)
                        </label>
                        <input type="number"
                               id="return_quantity"
                               name="quantity"
                               class="admin-dashboard-input"
                               min="1"
                               required>
                    </div>

                    <div>
                        <label for="return_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Retur <span class="text-red-500">*</span>
                        </label>
                        <select id="return_reason" name="reason" class="admin-dashboard-select" required>
                            <option value="">Pilih alasan retur</option>
                            <option value="damaged">Produk Rusak</option>
                            <option value="defective">Produk Cacat</option>
                            <option value="expired">Produk Kadaluarsa</option>
                            <option value="wrong_item">Produk Salah</option>
                            <option value="quality_issue">Masalah Kualitas</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="return_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Detail
                        </label>
                        <textarea id="return_description"
                                  name="description"
                                  rows="3"
                                  class="admin-dashboard-textarea"
                                  placeholder="Jelaskan detail masalah produk..."
                                  required></textarea>
                    </div>

                    <div>
                        <label for="return_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan Tambahan (Opsional)
                        </label>
                        <textarea id="return_notes"
                                  name="notes"
                                  rows="2"
                                  class="admin-dashboard-textarea"
                                  placeholder="Catatan tambahan untuk supplier..."></textarea>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeReturnModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Buat Retur
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function openReceiveModal(deliveryId, maxQuantity) {
    const modal = document.getElementById('receiveModal');
    const form = document.getElementById('receiveForm');
    const quantityInput = document.getElementById('received_quantity');
    const maxQuantitySpan = document.getElementById('maxQuantity');
    
    form.action = `/admin/supplier-deliveries/${deliveryId}/receive`;
    quantityInput.max = maxQuantity;
    quantityInput.value = maxQuantity;
    maxQuantitySpan.textContent = maxQuantity;
    
    modal.classList.add('active');
}

function closeReceiveModal() {
    const modal = document.getElementById('receiveModal');
    modal.classList.remove('active');
    document.getElementById('receiveForm').reset();
}

function openCancelModal(deliveryId) {
    const modal = document.getElementById('cancelModal');
    const form = document.getElementById('cancelForm');
    form.action = `/admin/supplier-deliveries/${deliveryId}/cancel`;
    modal.classList.add('active');
}

function closeCancelModal() {
    const modal = document.getElementById('cancelModal');
    modal.classList.remove('active');
    document.getElementById('cancelForm').reset();
}

function openReturnModal(deliveryId, productName, maxQuantity) {
    const modal = document.getElementById('returnModal');
    const deliveryIdInput = document.getElementById('return_delivery_id');
    const productNameElement = document.getElementById('returnProductName');
    const quantityInput = document.getElementById('return_quantity');
    const maxQuantitySpan = document.getElementById('maxReturnQuantity');

    deliveryIdInput.value = deliveryId;
    productNameElement.textContent = productName;
    quantityInput.max = maxQuantity;
    quantityInput.value = 1;
    maxQuantitySpan.textContent = maxQuantity;

    modal.classList.add('active');
}

function closeReturnModal() {
    const modal = document.getElementById('returnModal');
    modal.classList.remove('active');
    document.getElementById('returnForm').reset();
}

// Close modals when clicking outside
document.getElementById('receiveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReceiveModal();
    }
});

document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});

document.getElementById('returnModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReturnModal();
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/supplier-deliveries/index.blade.php ENDPATH**/ ?>