@extends('layouts.supplier')

@section('title', '<PERSON><PERSON><PERSON> - Dashboard Supplier')
@section('page-title', 'Kelola Retur')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Ke<PERSON>la Re<PERSON></h1>
                    <p class="text-gray-600 mt-1">Pantau dan kelola permintaan retur produk</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['total_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['requested_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Diminta</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['approved_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Disetujui</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['completed_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Selesai</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['cancelled_deliveries']) }}</div>
            <div class="supplier-dashboard-stat-label">Pengiriman Dibatalkan</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month"
                           name="month"
                           value="{{ $filterMonth }}"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari produk atau supplier..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="supplier-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Diminta</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="in_transit" {{ request('status') === 'in_transit' ? 'selected' : '' }}>Dalam Perjalanan</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                    </select>
                </div>

                <!-- Reason Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Alasan</label>
                    <select name="reason" class="supplier-dashboard-select">
                        <option value="">Semua Alasan</option>
                        <option value="damaged" {{ request('reason') === 'damaged' ? 'selected' : '' }}>Rusak</option>
                        <option value="expired" {{ request('reason') === 'expired' ? 'selected' : '' }}>Kadaluarsa</option>
                        <option value="defective" {{ request('reason') === 'defective' ? 'selected' : '' }}>Cacat</option>
                        <option value="overstock" {{ request('reason') === 'overstock' ? 'selected' : '' }}>Kelebihan Stok</option>
                        <option value="other" {{ request('reason') === 'other' ? 'selected' : '' }}>Lainnya</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Daftar Retur</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Asal</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                @if($return->supplier)
                                <div class="text-sm text-gray-500">Ke: {{ $return->supplier->name }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->source }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($return->quantity) }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->reason_in_indonesian }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ auth()->user()->formatDate($return->return_date) }}</div>
                                @if($return->approved_date)
                                <div class="text-sm text-gray-500">Disetujui: {{ auth()->user()->formatDate($return->approved_date) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    @if($return->status === 'requested') bg-yellow-100 text-yellow-800
                                    @elseif($return->status === 'approved') bg-blue-100 text-blue-800
                                    @elseif($return->status === 'in_transit') bg-purple-100 text-purple-800
                                    @elseif($return->status === 'completed') bg-green-100 text-green-800
                                    @elseif($return->status === 'rejected') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ $return->status_in_indonesian }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('supplier.returns.show', $return) }}" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    @if($return->status === 'approved')
                                    <button onclick="openRespondModal('{{ $return->id }}')" 
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Respons
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada retur</p>
                                    <p>Retur akan muncul di sini ketika ada permintaan dari gudang atau toko</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    <!-- Cancelled Deliveries Section -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Pengiriman yang Dibatalkan</h2>
            <p class="text-sm text-gray-600">Pengiriman yang dibatalkan oleh admin gudang</p>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Pengiriman</th>
                            <th class="px-6 py-3">Alasan Pembatalan</th>
                            <th class="px-6 py-3">Dibatalkan Oleh</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cancelledDeliveries as $delivery)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->product->name }}</div>
                                <div class="text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Pengiriman Dibatalkan
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ number_format($delivery->quantity) }} unit</div>
                                @if($delivery->unit_price)
                                <div class="text-xs text-gray-500">@ Rp {{ number_format($delivery->unit_price, 0, ',', '.') }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $delivery->delivery_date->format('d/m/Y') }}</div>
                                <div class="text-xs text-gray-500">{{ $delivery->delivery_date->diffForHumans() }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    {{ $delivery->notes ?: 'Tidak ada catatan' }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    {{ $delivery->receivedBy ? $delivery->receivedBy->name : 'Admin Gudang' }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="openResendModal('{{ $delivery->id }}', '{{ $delivery->product->name }}', '{{ $delivery->quantity }}')"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Kirim Ulang
                                    </button>
                                    <button onclick="openDeleteCancelledModal('{{ $delivery->id }}', '{{ $delivery->product->name }}')"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Hapus
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Tidak ada pengiriman yang dibatalkan</p>
                                    <p>Pengiriman yang dibatalkan akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div id="responseModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Respons Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeRespondModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="responseForm" method="POST">
            @csrf
            @method('PUT')
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Aksi</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="action" value="accept" class="mr-2" required>
                                <span class="text-green-600 font-medium">Terima Retur</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="action" value="reject" class="mr-2" required>
                                <span class="text-red-600 font-medium">Tolak Retur</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label for="supplier_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="supplier_notes"
                                  name="supplier_notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Tambahkan catatan untuk respons ini..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeRespondModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Kirim Respons
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Resend Cancelled Delivery Modal -->
<div id="resendModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Kirim Ulang Pengiriman</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeResendModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="resendForm" method="POST">
            @csrf
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-600 mb-4">
                            Buat pengiriman baru berdasarkan pengiriman yang dibatalkan untuk produk: <strong id="resendProductName"></strong>
                        </p>
                        <p class="text-sm text-gray-600 mb-4">
                            Jumlah: <strong id="resendQuantity"></strong> unit
                        </p>
                    </div>

                    <div>
                        <label for="resend_delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman Baru <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="resend_delivery_date"
                               name="delivery_date"
                               min="{{ date('Y-m-d') }}"
                               class="supplier-dashboard-input"
                               required>
                    </div>

                    <div>
                        <label for="resend_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="resend_notes"
                                  name="notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Catatan untuk pengiriman ulang..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeResendModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Buat Pengiriman Baru
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Cancelled Delivery Modal -->
<div id="deleteCancelledModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Hapus Pengiriman yang Dibatalkan</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteCancelledModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="deleteCancelledForm" method="POST">
            @csrf
            @method('DELETE')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penghapusan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menghapus pengiriman yang dibatalkan untuk produk <strong id="deleteProductName"></strong>?
                            Tindakan ini tidak dapat dibatalkan.
                        </p>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeDeleteCancelledModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    Ya, Hapus Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function openRespondModal(returnId) {
    const modal = document.getElementById('responseModal');
    const form = document.getElementById('responseForm');
    form.action = `/supplier/returns/${returnId}/respond`;
    modal.classList.add('active');
}

function closeRespondModal() {
    const modal = document.getElementById('responseModal');
    modal.classList.remove('active');
    document.getElementById('responseForm').reset();
}

function openResendModal(deliveryId, productName, quantity) {
    const modal = document.getElementById('resendModal');
    const form = document.getElementById('resendForm');
    const productNameElement = document.getElementById('resendProductName');
    const quantityElement = document.getElementById('resendQuantity');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function closeResendModal() {
    const modal = document.getElementById('resendModal');
    modal.classList.remove('active');
    document.getElementById('resendForm').reset();
}

function openDeleteCancelledModal(deliveryId, productName) {
    const modal = document.getElementById('deleteCancelledModal');
    const form = document.getElementById('deleteCancelledForm');
    const productNameElement = document.getElementById('deleteProductName');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteCancelledModal() {
    const modal = document.getElementById('deleteCancelledModal');
    modal.classList.remove('active');
}

// Close modals when clicking outside
document.getElementById('responseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRespondModal();
    }
});

document.getElementById('resendModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResendModal();
    }
});

document.getElementById('deleteCancelledModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteCancelledModal();
    }
});
</script>
@endpush
@endsection
